import jsPDF from 'jspdf';

interface PDFGeneratorOptions {
  resume: string;
  language: string;
  country: string;
}

export const generateEnhancedPDF = ({ resume, language, country }: PDFGeneratorOptions) => {
  const pdf = new jsPDF();
  const pageWidth = pdf.internal.pageSize.width;
  const pageHeight = pdf.internal.pageSize.height;
  const margin = 15;
  const maxWidth = pageWidth - (margin * 2);
  let y = margin;

  // Color scheme
  const primaryColor = [37, 99, 235]; // Blue
  const secondaryColor = [75, 85, 99]; // Gray
  const accentColor = [16, 185, 129]; // Green

  // Helper functions
  const addText = (text: string, x: number, yPos: number, options: any = {}) => {
    const {
      fontSize = 10,
      fontStyle = 'normal',
      color = [0, 0, 0],
      align = 'left',
      maxWidth: textMaxWidth = maxWidth
    } = options;

    pdf.setFontSize(fontSize);
    pdf.setFont('helvetica', fontStyle);
    pdf.setTextColor(color[0], color[1], color[2]);

    if (align === 'center') {
      const textWidth = pdf.getTextWidth(text);
      x = (pageWidth - textWidth) / 2;
    } else if (align === 'right') {
      const textWidth = pdf.getTextWidth(text);
      x = pageWidth - margin - textWidth;
    }

    const lines = pdf.splitTextToSize(text, textMaxWidth);
    lines.forEach((line: string, index: number) => {
      pdf.text(line, x, yPos + (index * (fontSize * 0.4)));
    });

    return yPos + (lines.length * (fontSize * 0.4));
  };

  const addLine = (x1: number, y1: number, x2: number, y2: number, color = primaryColor) => {
    pdf.setDrawColor(color[0], color[1], color[2]);
    pdf.setLineWidth(0.5);
    pdf.line(x1, y1, x2, y2);
  };

  const addSection = (title: string, content: string[], yPos: number) => {
    // Section header with line
    yPos += 8;
    pdf.setFontSize(12);
    pdf.setFont('helvetica', 'bold');
    pdf.setTextColor(primaryColor[0], primaryColor[1], primaryColor[2]);
    pdf.text(title, margin, yPos);
    
    // Underline
    const titleWidth = pdf.getTextWidth(title);
    addLine(margin, yPos + 2, margin + titleWidth + 10, yPos + 2, primaryColor);
    
    yPos += 6;

    // Content
    content.forEach(item => {
      if (yPos > pageHeight - 20) return; // Prevent overflow
      
      const cleanItem = item
        .replace(/\*\*(.*?)\*\*/g, '$1')
        .replace(/__(.*?)__/g, '$1')
        .replace(/\*(.*?)\*/g, '$1')
        .replace(/_(.*?)_/g, '$1')
        .replace(/`(.*?)`/g, '$1')
        .trim();

      if (cleanItem.startsWith('•') || cleanItem.startsWith('-')) {
        // Bullet point
        const bulletText = cleanItem.replace(/^[•\-]\s*/, '');
        pdf.setFontSize(9);
        pdf.setFont('helvetica', 'normal');
        pdf.setTextColor(secondaryColor[0], secondaryColor[1], secondaryColor[2]);
        
        // Add bullet
        pdf.setTextColor(primaryColor[0], primaryColor[1], primaryColor[2]);
        pdf.text('•', margin + 5, yPos);
        
        // Add text
        pdf.setTextColor(secondaryColor[0], secondaryColor[1], secondaryColor[2]);
        const lines = pdf.splitTextToSize(bulletText, maxWidth - 15);
        lines.forEach((line: string, index: number) => {
          pdf.text(line, margin + 12, yPos + (index * 4));
        });
        yPos += lines.length * 4 + 1;
      } else if (cleanItem.includes('|')) {
        // Job title with company
        const parts = cleanItem.split('|').map(p => p.trim());
        pdf.setFontSize(10);
        pdf.setFont('helvetica', 'bold');
        pdf.setTextColor(secondaryColor[0], secondaryColor[1], secondaryColor[2]);
        pdf.text(parts[0], margin, yPos);
        
        if (parts[1]) {
          pdf.setFont('helvetica', 'normal');
          pdf.setTextColor(primaryColor[0], primaryColor[1], primaryColor[2]);
          const titleWidth = pdf.getTextWidth(parts[0]);
          pdf.text(` | ${parts[1]}`, margin + titleWidth, yPos);
        }
        yPos += 5;
      } else {
        // Regular text
        pdf.setFontSize(9);
        pdf.setFont('helvetica', 'normal');
        pdf.setTextColor(secondaryColor[0], secondaryColor[1], secondaryColor[2]);
        const lines = pdf.splitTextToSize(cleanItem, maxWidth);
        lines.forEach((line: string, index: number) => {
          pdf.text(line, margin, yPos + (index * 4));
        });
        yPos += lines.length * 4 + 2;
      }
    });

    return yPos;
  };

  // Parse resume content
  const parseResume = (text: string) => {
    const lines = text.split('\n').map(line => line.trim()).filter(line => line);
    const sections: { [key: string]: string[] } = {};
    let currentSection = 'header';
    let headerInfo: string[] = [];

    lines.forEach((line, index) => {
      if (line.match(/^[A-Z\s&]+$/) && line.length > 3) {
        // Section header
        currentSection = line.toLowerCase().replace(/[^a-z]/g, '');
        sections[currentSection] = [];
      } else if (index < 5 && !line.match(/^[A-Z\s&]+$/)) {
        // Header information (first few lines)
        headerInfo.push(line);
      } else {
        if (!sections[currentSection]) {
          sections[currentSection] = [];
        }
        sections[currentSection].push(line);
      }
    });

    return { sections, headerInfo };
  };

  try {
    const { sections, headerInfo } = parseResume(resume);

    // Header section with name and contact
    if (headerInfo.length > 0) {
      // Name (first line)
      const name = headerInfo[0].replace(/\*\*/g, '').trim();
      pdf.setFontSize(18);
      pdf.setFont('helvetica', 'bold');
      pdf.setTextColor(primaryColor[0], primaryColor[1], primaryColor[2]);
      pdf.text(name, margin, y);
      y += 8;

      // Title/Role (second line if exists)
      if (headerInfo[1]) {
        const title = headerInfo[1].replace(/\*\*/g, '').trim();
        pdf.setFontSize(12);
        pdf.setFont('helvetica', 'normal');
        pdf.setTextColor(secondaryColor[0], secondaryColor[1], secondaryColor[2]);
        pdf.text(title, margin, y);
        y += 6;
      }

      // Contact information
      const contactInfo = headerInfo.slice(2).filter(line => 
        line.includes('@') || line.includes('phone') || line.includes('linkedin') || line.includes('github') || line.includes('+')
      );

      if (contactInfo.length > 0) {
        pdf.setFontSize(9);
        pdf.setFont('helvetica', 'normal');
        pdf.setTextColor(secondaryColor[0], secondaryColor[1], secondaryColor[2]);
        
        contactInfo.forEach((contact, index) => {
          const cleanContact = contact.replace(/\*\*/g, '').trim();
          pdf.text(cleanContact, margin, y + (index * 4));
        });
        y += contactInfo.length * 4 + 5;
      }

      // Separator line
      addLine(margin, y, pageWidth - margin, y, accentColor);
      y += 5;
    }

    // Add sections in order
    const sectionOrder = ['professionalsummary', 'keyskills', 'workexperience', 'education', 'certifications', 'additionalinformation'];
    
    sectionOrder.forEach(sectionKey => {
      if (sections[sectionKey] && sections[sectionKey].length > 0 && y < pageHeight - 30) {
        const sectionTitle = sectionKey
          .replace(/([a-z])([A-Z])/g, '$1 $2')
          .replace(/^./, str => str.toUpperCase())
          .replace(/summary/i, 'Summary')
          .replace(/skills/i, 'Skills')
          .replace(/experience/i, 'Experience')
          .replace(/education/i, 'Education')
          .replace(/information/i, 'Information');

        y = addSection(sectionTitle, sections[sectionKey], y);
      }
    });

    // Footer
    const footerY = pageHeight - 10;
    pdf.setFontSize(7);
    pdf.setFont('helvetica', 'normal');
    pdf.setTextColor(150, 150, 150);
    const footerText = `Generated for ${country} market | ${new Date().toLocaleDateString()} | AI-Optimized Resume`;
    const footerWidth = pdf.getTextWidth(footerText);
    pdf.text(footerText, (pageWidth - footerWidth) / 2, footerY);

    // Save the PDF
    const fileName = `Resume_${country}_${language.toUpperCase()}_${new Date().toISOString().split('T')[0]}.pdf`;
    pdf.save(fileName);

    return true;
  } catch (error) {
    console.error('Enhanced PDF generation error:', error);
    throw new Error('Failed to generate enhanced PDF');
  }
};
