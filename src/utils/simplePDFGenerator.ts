import jsPDF from 'jspdf';

interface SimplePDFOptions {
  resume: string;
  language: string;
  country: string;
}

// Extract name and role for filename
const extractNameAndRole = (resume: string): { name: string; role: string } => {
  const lines = resume.split('\n').map(line => line.trim()).filter(line => line);
  
  let name = 'resume';
  if (lines.length > 0) {
    name = lines[0]
      .replace(/\*\*/g, '')
      .replace(/[^a-zA-Z\s]/g, '')
      .trim()
      .split(' ')
      .slice(0, 2)
      .join('_')
      .toLowerCase();
  }
  
  let role = 'professional';
  const roleKeywords = [
    'engineer', 'developer', 'architect', 'manager', 'lead', 'senior', 'junior',
    'analyst', 'consultant', 'specialist', 'coordinator', 'director', 'designer',
    'full stack', 'backend', 'frontend', 'software', 'web', 'mobile', 'devops',
    'data', 'machine learning', 'ai', 'blockchain', 'cloud'
  ];
  
  const resumeText = resume.toLowerCase();
  for (const keyword of roleKeywords) {
    if (resumeText.includes(keyword)) {
      role = keyword.replace(/\s+/g, '_');
      break;
    }
  }
  
  return { name: name || 'resume', role };
};

export const generateSimplePDF = ({ resume, language, country }: SimplePDFOptions) => {
  try {
    const pdf = new jsPDF();
    const pageWidth = pdf.internal.pageSize.width;
    const pageHeight = pdf.internal.pageSize.height;
    const margin = 15;
    const maxWidth = pageWidth - (margin * 2);
    let y = margin;

    // Colors
    const primaryColor = [41, 98, 255];
    const secondaryColor = [55, 65, 81];
    const accentColor = [16, 185, 129];

    // Clean text helper
    const cleanText = (text: string): string => {
      return text
        .replace(/\*\*(.*?)\*\*/g, '$1')
        .replace(/__(.*?)__/g, '$1')
        .replace(/\*(.*?)\*/g, '$1')
        .replace(/_(.*?)_/g, '$1')
        .replace(/`(.*?)`/g, '$1')
        .trim();
    };

    // Add text helper
    const addText = (text: string, x: number, yPos: number, fontSize: number = 10, fontStyle: string = 'normal', color: number[] = secondaryColor): number => {
      const cleanedText = cleanText(text);
      if (!cleanedText) return yPos;

      pdf.setFontSize(fontSize);
      pdf.setFont('helvetica', fontStyle);
      pdf.setTextColor(color[0], color[1], color[2]);

      const lines = pdf.splitTextToSize(cleanedText, maxWidth);
      lines.forEach((line: string, index: number) => {
        if (yPos + (index * 5) < pageHeight - 20) {
          pdf.text(line, x, yPos + (index * 5));
        }
      });

      return yPos + (lines.length * 5) + 2;
    };

    // Parse resume into lines
    const lines = resume.split('\n').map(line => line.trim()).filter(line => line);
    
    console.log('Processing resume with', lines.length, 'lines');

    let isFirstLine = true;
    let lineIndex = 0;

    for (const line of lines) {
      if (y > pageHeight - 30) break; // Prevent overflow

      const cleanLine = cleanText(line);
      if (!cleanLine) continue;

      // Detect different types of content
      if (cleanLine.match(/^[A-Z\s&]+$/) && cleanLine.length > 3) {
        // Section headers
        y += 8;
        pdf.setDrawColor(accentColor[0], accentColor[1], accentColor[2]);
        pdf.setLineWidth(1);
        pdf.line(margin, y - 2, margin + 50, y - 2);
        
        y = addText(cleanLine, margin, y, 12, 'bold', primaryColor);
        y += 3;
        
      } else if (isFirstLine) {
        // Name (first line)
        y = addText(cleanLine, margin, y, 18, 'bold', primaryColor);
        y += 5;
        isFirstLine = false;
        
      } else if (lineIndex === 1) {
        // Title/role (second line)
        y = addText(cleanLine, margin, y, 12, 'normal', secondaryColor);
        y += 5;
        
      } else if (cleanLine.includes('@') || cleanLine.includes('+') || cleanLine.includes('linkedin') || cleanLine.includes('github')) {
        // Contact info
        y = addText(cleanLine, margin, y, 9, 'normal', [100, 100, 100]);
        
      } else if (cleanLine.includes('|')) {
        // Job titles with company
        y = addText(cleanLine, margin, y, 10, 'bold', secondaryColor);
        y += 2;
        
      } else if (cleanLine.startsWith('•') || cleanLine.startsWith('-') || cleanLine.startsWith('*')) {
        // Bullet points
        const bulletText = cleanLine.replace(/^[•\-\*]\s*/, '');
        pdf.setFontSize(8);
        pdf.setFont('helvetica', 'normal');
        pdf.setTextColor(accentColor[0], accentColor[1], accentColor[2]);
        pdf.text('•', margin + 5, y);
        
        y = addText(bulletText, margin + 12, y - 2, 9, 'normal', secondaryColor);
        
      } else {
        // Regular text
        y = addText(cleanLine, margin, y, 9, 'normal', secondaryColor);
      }

      lineIndex++;
    }

    // Footer
    pdf.setFontSize(7);
    pdf.setFont('helvetica', 'normal');
    pdf.setTextColor(150, 150, 150);
    const footerText = `AI-Optimized for ${country} | Generated ${new Date().toLocaleDateString()}`;
    const footerWidth = pdf.getTextWidth(footerText);
    pdf.text(footerText, (pageWidth - footerWidth) / 2, pageHeight - 10);

    // Generate filename
    const { name, role } = extractNameAndRole(resume);
    const countryCode = country.toLowerCase().replace(/[^a-z]/g, '').replace('international', '');
    const fileName = countryCode ? `${name}_${role}_${countryCode}_resume.pdf` : `${name}_${role}_resume.pdf`;

    console.log('Saving PDF as:', fileName);
    pdf.save(fileName);
    
    return true;
  } catch (error) {
    console.error('Simple PDF generation error:', error);
    throw new Error('Failed to generate PDF');
  }
};
