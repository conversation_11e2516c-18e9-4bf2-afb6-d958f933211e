import { GoogleGenerativeA<PERSON> } from '@google/generative-ai';

// Initialize the Gemini AI client
const getGeminiClient = () => {
  const apiKey = import.meta.env.VITE_GEMINI_API_KEY;
  
  if (!apiKey) {
    throw new Error('VITE_GEMINI_API_KEY is not configured. Please add your Gemini API key to the environment variables.');
  }
  
  return new GoogleGenerativeAI(apiKey);
};

export interface ResumeGenerationRequest {
  jobDescription: string;
  baseResume?: string;
  editPrompt?: string;
}

export const generateResumeWithAI = async ({
  jobDescription,
  baseResume,
  editPrompt
}: ResumeGenerationRequest): Promise<string> => {
  try {
    const genAI = getGeminiClient();
    const model = genAI.getGenerativeModel({ model: "gemini-1.5-flash" });

    let prompt = '';

    if (editPrompt && baseResume) {
      // This is an edit request
      prompt = `
You are an expert ATS-optimized resume writer and career consultant. I need you to enhance an existing resume based on specific feedback while maintaining ATS compatibility.

CURRENT RESUME:
${baseResume}

ENHANCEMENT REQUEST:
${editPrompt}

JOB DESCRIPTION FOR CONTEXT:
${jobDescription}

Please enhance the resume according to the request while ensuring:
1. ATS-friendly formatting with clear section headers
2. Strategic keyword integration from the job description
3. Quantified achievements where possible
4. Professional language and industry-standard terminology
5. Optimal length and readability for both ATS and human reviewers

Format the resume with these sections:
- PERSONAL INFORMATION
- PROFESSIONAL SUMMARY
- KEY SKILLS
- WORK EXPERIENCE
- EDUCATION & CERTIFICATIONS
- ADDITIONAL INFORMATION

Return only the enhanced resume content in a clean, professional format. Do not include any explanations or meta-text.
      `;
    } else if (baseResume) {
      // Generate resume based on uploaded resume and job description
      prompt = `
You are an expert ATS-optimized resume writer and career consultant. Create a tailored, ATS-friendly resume that will pass through applicant tracking systems and impress hiring managers.

ORIGINAL RESUME:
${baseResume}

TARGET JOB DESCRIPTION:
${jobDescription}

Create a professionally optimized resume that:

1. **ATS OPTIMIZATION:**
   - Use standard section headers (PERSONAL INFORMATION, PROFESSIONAL SUMMARY, KEY SKILLS, WORK EXPERIENCE, EDUCATION & CERTIFICATIONS, ADDITIONAL INFORMATION)
   - Incorporate relevant keywords from the job description naturally
   - Use simple, clean formatting without tables or graphics
   - Include industry-standard terminology

2. **CONTENT ENHANCEMENT:**
   - Extract and highlight the most relevant experience from the original resume
   - Quantify achievements with specific numbers, percentages, or metrics
   - Emphasize transferable skills that match job requirements
   - Create a compelling professional summary that aligns with the target role
   - Prioritize recent and relevant experience

3. **STRATEGIC POSITIONING:**
   - Lead with strongest qualifications that match the job requirements
   - Use action verbs and achievement-focused language
   - Demonstrate impact and results in previous roles
   - Show career progression and growth

4. **PROFESSIONAL FORMATTING:**
   - Maintain consistent formatting throughout
   - Use bullet points for easy scanning
   - Keep to 1-2 pages maximum
   - Ensure readability for both ATS and human reviewers

Return only the optimized resume content in a clean, professional format. Do not include any explanations or meta-text.
      `;
    } else {
      // Generate resume from scratch based only on job description
      prompt = `
You are an expert ATS-optimized resume writer and career consultant. Create a professional resume template optimized for applicant tracking systems based on the provided job description.

TARGET JOB DESCRIPTION:
${jobDescription}

Create a comprehensive, ATS-friendly resume template that:

1. **ATS OPTIMIZATION:**
   - Use standard section headers that ATS systems recognize
   - Incorporate relevant keywords from the job description
   - Use simple, clean formatting without complex layouts
   - Include industry-standard terminology and phrases

2. **PROFESSIONAL STRUCTURE:**
   - PERSONAL INFORMATION (Name, Phone, Email, Location, LinkedIn)
   - PROFESSIONAL SUMMARY (3-4 lines highlighting key qualifications)
   - KEY SKILLS (Technical and soft skills relevant to the role)
   - WORK EXPERIENCE (3-4 relevant positions with achievements)
   - EDUCATION & CERTIFICATIONS (Relevant degrees and certifications)
   - ADDITIONAL INFORMATION (Languages, projects, volunteer work if relevant)

3. **CONTENT GUIDELINES:**
   - Create realistic but impressive achievements with quantified results
   - Use action verbs and achievement-focused language
   - Demonstrate progression and growth in career
   - Show relevant skills and technologies mentioned in job description
   - Include industry-specific terminology

4. **FORMATTING STANDARDS:**
   - Clean, professional layout
   - Consistent formatting throughout
   - Easy to scan with bullet points
   - Optimized for both ATS parsing and human readability

Return only the professional resume template content. Do not include any explanations or meta-text.
      `;
    }

    const result = await model.generateContent(prompt);
    const response = await result.response;
    const text = response.text();

    if (!text || text.trim().length === 0) {
      throw new Error('AI service returned empty response');
    }

    return text.trim();
  } catch (error) {
    console.error('Error generating resume with AI:', error);
    
    if (error instanceof Error) {
      if (error.message.includes('API key')) {
        throw new Error('AI service configuration error. Please check your API key.');
      } else if (error.message.includes('quota') || error.message.includes('limit')) {
        throw new Error('AI service quota exceeded. Please try again later.');
      } else if (error.message.includes('network') || error.message.includes('fetch')) {
        throw new Error('Network error. Please check your internet connection and try again.');
      }
    }
    
    throw new Error('Failed to generate resume with AI. Please try again.');
  }
};

export const validateApiKey = async (): Promise<boolean> => {
  try {
    const genAI = getGeminiClient();
    const model = genAI.getGenerativeModel({ model: "gemini-1.5-flash" });
    
    // Test with a simple prompt
    const result = await model.generateContent("Hello");
    const response = await result.response;
    const text = response.text();
    
    return text && text.length > 0;
  } catch (error) {
    console.error('API key validation failed:', error);
    return false;
  }
};
