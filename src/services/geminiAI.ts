import { GoogleGenerativeAI } from '@google/generative-ai';

// Initialize the Gemini AI client
const getGeminiClient = () => {
  const apiKey = import.meta.env.VITE_GEMINI_API_KEY;
  
  if (!apiKey) {
    throw new Error('VITE_GEMINI_API_KEY is not configured. Please add your Gemini API key to the environment variables.');
  }
  
  return new GoogleGenerativeAI(apiKey);
};

export interface ResumeGenerationRequest {
  jobDescription: string;
  baseResume?: string;
  editPrompt?: string;
}

export const generateResumeWithAI = async ({
  jobDescription,
  baseResume,
  editPrompt
}: ResumeGenerationRequest): Promise<string> => {
  try {
    const genAI = getGeminiClient();
    const model = genAI.getGenerativeModel({ model: "gemini-1.5-flash" });

    let prompt = '';

    if (editPrompt && baseResume) {
      // This is an edit request
      prompt = `
You are an expert resume writer and career consultant. I need you to update an existing resume based on specific feedback.

CURRENT RESUME:
${baseResume}

EDIT REQUEST:
${editPrompt}

JOB DESCRIPTION FOR CONTEXT:
${jobDescription}

Please update the resume according to the edit request while keeping it relevant to the job description. Return only the updated resume content in a clean, professional format. Do not include any explanations or meta-text.
      `;
    } else if (baseResume) {
      // Generate resume based on uploaded resume and job description
      prompt = `
You are an expert resume writer and career consultant. I need you to create a tailored resume based on an existing resume and a specific job description.

ORIGINAL RESUME:
${baseResume}

JOB DESCRIPTION:
${jobDescription}

Please create a new, tailored resume that:
1. Uses the experience, skills, and education from the original resume
2. Emphasizes the most relevant qualifications for this specific job
3. Incorporates keywords from the job description naturally
4. Quantifies achievements where possible
5. Maintains a professional, ATS-friendly format
6. Highlights transferable skills that match the job requirements

Return only the tailored resume content in a clean, professional format. Do not include any explanations or meta-text.
      `;
    } else {
      // Generate resume from scratch based only on job description
      prompt = `
You are an expert resume writer and career consultant. I need you to create a professional resume template based on a job description.

JOB DESCRIPTION:
${jobDescription}

Please create a comprehensive resume template that:
1. Includes all sections typically expected for this type of role
2. Incorporates relevant keywords from the job description
3. Suggests appropriate skills and qualifications
4. Provides a professional format that's ATS-friendly
5. Includes placeholder content that can be customized

Return only the resume template content in a clean, professional format. Do not include any explanations or meta-text.
      `;
    }

    const result = await model.generateContent(prompt);
    const response = await result.response;
    const text = response.text();

    if (!text || text.trim().length === 0) {
      throw new Error('AI service returned empty response');
    }

    return text.trim();
  } catch (error) {
    console.error('Error generating resume with AI:', error);
    
    if (error instanceof Error) {
      if (error.message.includes('API key')) {
        throw new Error('AI service configuration error. Please check your API key.');
      } else if (error.message.includes('quota') || error.message.includes('limit')) {
        throw new Error('AI service quota exceeded. Please try again later.');
      } else if (error.message.includes('network') || error.message.includes('fetch')) {
        throw new Error('Network error. Please check your internet connection and try again.');
      }
    }
    
    throw new Error('Failed to generate resume with AI. Please try again.');
  }
};

export const validateApiKey = async (): Promise<boolean> => {
  try {
    const genAI = getGeminiClient();
    const model = genAI.getGenerativeModel({ model: "gemini-1.5-flash" });
    
    // Test with a simple prompt
    const result = await model.generateContent("Hello");
    const response = await result.response;
    const text = response.text();
    
    return text && text.length > 0;
  } catch (error) {
    console.error('API key validation failed:', error);
    return false;
  }
};
