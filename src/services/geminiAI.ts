import { GoogleGenerativeAI } from '@google/generative-ai';

// Initialize the Gemini AI client
const getGeminiClient = () => {
  const apiKey = import.meta.env.VITE_GEMINI_API_KEY;
  
  if (!apiKey) {
    throw new Error('VITE_GEMINI_API_KEY is not configured. Please add your Gemini API key to the environment variables.');
  }
  
  return new GoogleGenerativeAI(apiKey);
};

export interface ResumeGenerationRequest {
  jobDescription: string;
  baseResume?: string;
  editPrompt?: string;
  language?: string;
  country?: string;
}

export const generateResumeWithAI = async ({
  jobDescription,
  baseResume,
  editPrompt,
  language = 'en',
  country = 'International'
}: ResumeGenerationRequest): Promise<string> => {
  try {
    const genAI = getGeminiClient();
    const model = genAI.getGenerativeModel({ model: "gemini-1.5-flash" });

    // Get language-specific formatting and cultural guidelines
    const getLanguageGuidelines = (lang: string, country: string) => {
      const guidelines = {
        en: {
          format: 'Standard international resume format',
          length: 'Keep to 1 page maximum for optimal impact',
          style: 'Professional, concise, achievement-focused',
          cultural: 'Emphasize individual achievements, quantified results, and career progression',
          sections: 'CONTACT INFO, PROFESSIONAL SUMMARY, KEY SKILLS, WORK EXPERIENCE, EDUCATION, CERTIFICATIONS'
        },
        ja: {
          format: 'Japanese Rirekisho (履歴書) format adapted for IT professionals',
          length: 'Concise 1-page format respecting Japanese business culture',
          style: 'Formal, respectful, team-oriented achievements',
          cultural: 'Emphasize company loyalty, team collaboration, continuous learning, and respect for hierarchy',
          sections: '個人情報 (Personal Info), 職歴要約 (Career Summary), 技術スキル (Technical Skills), 職歴 (Work Experience), 学歴 (Education), 資格 (Certifications)'
        },
        es: {
          format: 'Spanish CV format following European standards',
          length: '1 page maximum, well-structured and detailed',
          style: 'Professional, educational background emphasis',
          cultural: 'Highlight education, language skills, international experience, and cultural adaptability',
          sections: 'INFORMACIÓN PERSONAL, RESUMEN PROFESIONAL, COMPETENCIAS TÉCNICAS, EXPERIENCIA LABORAL, FORMACIÓN, IDIOMAS'
        },
        fr: {
          format: 'French CV format following French professional standards',
          length: '1 page maximum, elegant and structured',
          style: 'Sophisticated, education-focused, detailed',
          cultural: 'Emphasize educational achievements, intellectual capabilities, and methodical approach',
          sections: 'INFORMATIONS PERSONNELLES, PROFIL PROFESSIONNEL, COMPÉTENCES TECHNIQUES, EXPÉRIENCE PROFESSIONNELLE, FORMATION, LANGUES'
        },
        de: {
          format: 'German Lebenslauf format for IT professionals',
          length: '1 page maximum, comprehensive and detailed',
          style: 'Thorough, systematic, qualification-focused',
          cultural: 'Emphasize qualifications, systematic approach, precision, and technical expertise',
          sections: 'PERSÖNLICHE DATEN, BERUFSPROFIL, TECHNISCHE FÄHIGKEITEN, BERUFSERFAHRUNG, AUSBILDUNG, ZERTIFIZIERUNGEN'
        }
      };
      return guidelines[lang as keyof typeof guidelines] || guidelines.en;
    };

    const langGuidelines = getLanguageGuidelines(language, country);
    let prompt = '';

    if (editPrompt && baseResume) {
      // This is an edit request
      prompt = `
You are an expert ATS-optimized resume writer and career consultant. I need you to enhance an existing resume based on specific feedback while maintaining ATS compatibility.

CURRENT RESUME:
${baseResume}

ENHANCEMENT REQUEST:
${editPrompt}

JOB DESCRIPTION FOR CONTEXT:
${jobDescription}

Please enhance the resume according to the request while ensuring:
1. ATS-friendly formatting with clear section headers
2. Strategic keyword integration from the job description
3. Quantified achievements where possible
4. Professional language and industry-standard terminology
5. Optimal length and readability for both ATS and human reviewers

Format the resume with these sections:
- PERSONAL INFORMATION
- PROFESSIONAL SUMMARY
- KEY SKILLS
- WORK EXPERIENCE
- EDUCATION & CERTIFICATIONS
- ADDITIONAL INFORMATION

Return only the enhanced resume content in a clean, professional format. Do not include any explanations or meta-text.
      `;
    } else if (baseResume) {
      // Generate resume based on uploaded resume and job description
      prompt = `
You are an expert international resume writer specializing in IT professionals seeking global opportunities. Create a highly tailored, culturally-appropriate resume for ${country} in ${language === 'en' ? 'English' : language === 'ja' ? 'Japanese' : language === 'es' ? 'Spanish' : language === 'fr' ? 'French' : 'German'}.

ORIGINAL RESUME:
${baseResume}

TARGET JOB DESCRIPTION:
${jobDescription}

TARGET MARKET: ${country} (${language.toUpperCase()})
LANGUAGE: ${language === 'en' ? 'English' : language === 'ja' ? 'Japanese' : language === 'es' ? 'Spanish' : language === 'fr' ? 'French' : 'German'}
CULTURAL FORMAT: ${langGuidelines.format}
PROFESSIONAL STYLE: ${langGuidelines.style}

**STEP 1: ANALYZE THE JOB REQUIREMENTS**
First, carefully extract from the job description:
- Exact job title and seniority level
- Required programming languages and technologies
- Years of experience needed
- Key responsibilities and duties
- Company culture and values
- Required skills vs. bonus skills
- Industry context (Web3, NFT, blockchain, etc.)

**STEP 2: CREATE PERFECT ALIGNMENT**
Transform the original resume to perfectly match this specific job by:

1. **PROFESSIONAL SUMMARY ALIGNMENT:**
   - Mirror the job title and required experience level
   - Include the exact technologies mentioned in the job description
   - Reflect the company's industry and values
   - Show enthusiasm for the specific domain (Web3, blockchain, etc.)

2. **SKILLS OPTIMIZATION:**
   - Prioritize technologies mentioned in the job description
   - Group skills to match the job's technical requirements
   - Highlight frameworks, databases, and tools specifically mentioned
   - Include any blockchain/Web3 technologies if mentioned

3. **EXPERIENCE REFRAMING:**
   - Emphasize work experience that matches the job responsibilities
   - Quantify achievements relevant to the role requirements
   - Highlight backend engineering, scalable systems, microservices
   - Show progression in the required technologies
   - Demonstrate problem-solving and collaboration skills

4. **COMPANY CULTURE MATCH:**
   - Reflect startup experience if it's a startup
   - Highlight remote work experience if it's remote
   - Show passion for the industry (Web3, NFT, blockchain)
   - Demonstrate values alignment

5. **KEYWORD INTEGRATION:**
   - Naturally incorporate exact terms from the job description
   - Use the same technology names and frameworks
   - Include industry-specific terminology
   - Match the language style and tone

**CULTURAL & LANGUAGE REQUIREMENTS:**
- Write in ${language === 'en' ? 'English' : language === 'ja' ? 'Japanese' : language === 'es' ? 'Spanish' : language === 'fr' ? 'French' : 'German'} following ${country} professional standards
- Use section headers: ${langGuidelines.sections}
- Follow cultural expectations: ${langGuidelines.cultural}
- Maintain ${langGuidelines.length}
- Adapt tone and style for ${country} job market

**INTERNATIONAL IT PROFESSIONAL FOCUS:**
- Emphasize global experience and cross-cultural collaboration
- Highlight technical skills relevant to international markets
- Show adaptability and willingness to work internationally
- Demonstrate language skills and cultural awareness
- Include visa status or work authorization if relevant

**FORMATTING REQUIREMENTS:**
- Use **bold** for job titles, company names, and key technologies
- Use bullet points (•) for achievements and responsibilities
- Use appropriate section headers for the target country
- Include contact information clearly formatted
- Use | to separate job details
- Ensure ${langGuidelines.length}

**CRITICAL SUCCESS FACTORS:**
- Perfect cultural fit for ${country} job market
- Native-level ${language === 'en' ? 'English' : language === 'ja' ? 'Japanese' : language === 'es' ? 'Spanish' : language === 'fr' ? 'French' : 'German'} professional writing
- Every line contributes to getting THIS specific international job
- Use exact keywords and phrases from the job description
- Show clear alignment with required experience level
- Demonstrate passion for international IT opportunities
- Position as ideal candidate for global tech companies

Return only the perfectly tailored resume content in ${language === 'en' ? 'English' : language === 'ja' ? 'Japanese' : language === 'es' ? 'Spanish' : language === 'fr' ? 'French' : 'German'} that will get this candidate shortlisted for this specific position in ${country}.
      `;
    } else {
      // Generate resume from scratch based only on job description
      prompt = `
You are an expert ATS-optimized resume writer and career consultant. Create a professional resume template optimized for applicant tracking systems based on the provided job description.

TARGET JOB DESCRIPTION:
${jobDescription}

Create a comprehensive, ATS-friendly resume template that:

1. **ATS OPTIMIZATION:**
   - Use standard section headers that ATS systems recognize
   - Incorporate relevant keywords from the job description
   - Use simple, clean formatting without complex layouts
   - Include industry-standard terminology and phrases

2. **PROFESSIONAL STRUCTURE:**
   - PERSONAL INFORMATION (Name, Phone, Email, Location, LinkedIn)
   - PROFESSIONAL SUMMARY (3-4 lines highlighting key qualifications)
   - KEY SKILLS (Technical and soft skills relevant to the role)
   - WORK EXPERIENCE (3-4 relevant positions with achievements)
   - EDUCATION & CERTIFICATIONS (Relevant degrees and certifications)
   - ADDITIONAL INFORMATION (Languages, projects, volunteer work if relevant)

3. **CONTENT GUIDELINES:**
   - Create realistic but impressive achievements with quantified results
   - Use action verbs and achievement-focused language
   - Demonstrate progression and growth in career
   - Show relevant skills and technologies mentioned in job description
   - Include industry-specific terminology

4. **FORMATTING STANDARDS:**
   - Use **bold text** for job titles, company names, and key achievements
   - Use bullet points (•) for lists and accomplishments
   - Use ALL CAPS for section headers
   - Clean, professional layout
   - Consistent formatting throughout
   - Easy to scan with bullet points
   - Optimized for both ATS parsing and human readability

**FORMATTING GUIDELINES:**
- Use **bold** for emphasis on important terms and achievements
- Use bullet points (•) for all lists
- Use ALL CAPS for section headers (PERSONAL INFORMATION, PROFESSIONAL SUMMARY, etc.)
- Include realistic contact information
- Use | to separate job details (Title | Company | Dates)

Return only the professional resume template content with proper formatting. Do not include any explanations or meta-text.
      `;
    }

    const result = await model.generateContent(prompt);
    const response = await result.response;
    const text = response.text();

    if (!text || text.trim().length === 0) {
      throw new Error('AI service returned empty response');
    }

    return text.trim();
  } catch (error) {
    console.error('Error generating resume with AI:', error);
    
    if (error instanceof Error) {
      if (error.message.includes('API key')) {
        throw new Error('AI service configuration error. Please check your API key.');
      } else if (error.message.includes('quota') || error.message.includes('limit')) {
        throw new Error('AI service quota exceeded. Please try again later.');
      } else if (error.message.includes('network') || error.message.includes('fetch')) {
        throw new Error('Network error. Please check your internet connection and try again.');
      }
    }
    
    throw new Error('Failed to generate resume with AI. Please try again.');
  }
};

export const validateApiKey = async (): Promise<boolean> => {
  try {
    const genAI = getGeminiClient();
    const model = genAI.getGenerativeModel({ model: "gemini-1.5-flash" });
    
    // Test with a simple prompt
    const result = await model.generateContent("Hello");
    const response = await result.response;
    const text = response.text();
    
    return text && text.length > 0;
  } catch (error) {
    console.error('API key validation failed:', error);
    return false;
  }
};
